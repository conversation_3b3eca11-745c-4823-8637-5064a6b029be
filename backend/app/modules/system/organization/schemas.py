from datetime import datetime
from typing import Optional

from fastapi import Query
from pydantic import BaseModel, Field, validator
import re


class OrganizationBase(BaseModel):
    """机构基础模型"""

    code: str = Field(..., description="机构唯一标识", min_length=1, max_length=50)
    name: str = Field(..., description="机构名称", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="机构简介")
    status: bool = Field(True, description="机构状态：True=启用，False=停用")

    @validator("code")
    def validate_code(cls, v):
        """验证机构标识格式：大写字母开头，支持大写字母、下划线和数字"""
        if not re.match(r"^[A-Z][A-Z0-9_]*$", v):
            raise ValueError("机构标识必须以大写字母开头，只能包含大写字母、数字和下划线")
        return v


class OrganizationCreate(OrganizationBase):
    """机构创建模型"""
    pass


class OrganizationUpdate(BaseModel):
    """机构更新模型"""

    name: Optional[str] = Field(None, description="机构名称", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="机构简介")
    status: Optional[bool] = Field(None, description="机构状态：True=启用，False=停用")


class Organization(OrganizationBase):
    """返回给API的机构模型"""

    id: int = Field(..., description="机构ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class OrganizationListParams(BaseModel):
    """机构列表查询参数"""

    search: Optional[str] = Field(None, description="搜索关键词，支持机构名称和标识搜索")


def get_organization_list_params(
    search: Optional[str] = Query(None, description="搜索关键词，支持机构名称和标识搜索")
) -> OrganizationListParams:
    """获取机构列表查询参数"""
    return OrganizationListParams(search=search)
