from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.modules.system.organization.schemas import (
    Organization,
    OrganizationCreate,
    OrganizationUpdate,
    OrganizationListParams,
    get_organization_list_params,
)
from app.modules.system.organization.service import OrganizationService
from app.core.pagination import PageResponse, get_page_params, PageParams
from app.modules.system.user.deps import get_current_user
from app.modules.system.user.schemas import User

router = APIRouter()


@router.post("/", response_model=Organization, summary="创建机构")
async def create_organization(
    params: OrganizationCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    创建机构接口

    - code: 机构唯一标识（大写字母开头，支持大写字母、下划线和数字）
    - name: 机构名称
    - description: 机构简介（可选）
    - status: 机构状态（默认启用）

    只有超级管理员可以访问
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以创建机构",
        )
    return await OrganizationService.create(db, params)


@router.get("/", response_model=PageResponse[Organization], summary="获取机构列表")
async def get_organization_list(
    db: AsyncSession = Depends(get_db),
    page_params: PageParams = Depends(get_page_params),
    query_params: OrganizationListParams = Depends(get_organization_list_params),
    current_user: User = Depends(get_current_user),
):
    """
    获取机构列表接口

    支持分页和搜索功能：
    - page: 页码，从1开始
    - size: 每页数量，最大100
    - search: 搜索关键词，支持机构名称和标识搜索

    只有超级管理员可以访问
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以查看机构列表",
        )
    return await OrganizationService.get_list(db, page_params, query_params)


@router.get("/{organization_id}", response_model=Organization, summary="获取机构详情")
async def get_organization(
    organization_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取机构详情接口

    只有超级管理员可以访问
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以查看机构详情",
        )
    
    organization = await OrganizationService.get_by_id(db, organization_id)
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="机构不存在",
        )
    return organization


@router.put("/{organization_id}", response_model=Organization, summary="更新机构")
async def update_organization(
    organization_id: int,
    params: OrganizationUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    更新机构接口

    只有超级管理员可以访问
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以更新机构",
        )
    return await OrganizationService.update(db, organization_id, params)


@router.delete("/{organization_id}", summary="删除机构")
async def delete_organization(
    organization_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    删除机构接口

    前置检查：若机构下存在用户，需先解除所有用户关联
    级联删除：删除所有角色和权限配置

    只有超级管理员可以访问
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以删除机构",
        )
    
    await OrganizationService.delete(db, organization_id)
    return {"message": "机构删除成功"}
