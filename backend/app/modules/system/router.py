from .user.router import router as user_router
from .organization.router import router as organization_router
from .role.router import router as role_router
from .permission.router import router as permission_router
from fastapi import APIRouter

api_router = APIRouter()
api_router.include_router(user_router, prefix="/user", tags=["user"])
api_router.include_router(organization_router, prefix="/organization", tags=["organization"])
api_router.include_router(role_router, prefix="/role", tags=["role"])
api_router.include_router(permission_router, prefix="/permission", tags=["permission"])
