from datetime import <PERSON><PERSON><PERSON>
from typing import Optional

from fastapi import HTTPException, status
from pydantic import EmailStr
from sqlalchemy import func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import (
    ACCESS_TOKEN_EXPIRE_MINUTES,
    create_access_token,
    get_password_hash,
    verify_password,
)
from app.models.system.user import User as DBUser
from app.models.system.organization import Organization as DBOrganization
from app.models.system.role import Role as DBRole
from app.models.system.user_organization import UserOrganization as DBUserOrganization
from app.modules.system.user.schemas import (
    Token,
    User,
    UserCreate,
    UserListParams,
    UserWithOrganizations,
    UserOrganizationInfo,
    UserOrganizationAssign,
    UserOrganizationUpdate,
)
from app.core.pagination import PageParams, PageResponse, paginate_query


class UserService:
    """用户服务类"""

    @staticmethod
    async def get_by_username(db: AsyncSession, username: str) -> Optional[User]:
        """
        通过用户名获取用户

        Args:
            db: 数据库会话
            username: 用户名

        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        result = await db.execute(select(DBUser).filter(DBUser.username == username))
        user = result.scalars().first()
        if user:
            return User.model_validate(user)
        return None

    @staticmethod
    async def get_by_email(db: AsyncSession, email: EmailStr) -> Optional[User]:
        """
        通过邮箱获取用户

        Args:
            db: 数据库会话
            email: 邮箱

        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        if not email:
            return None
        result = await db.execute(select(DBUser).filter(DBUser.email == email))
        user = result.scalars().first()
        if user:
            return User.model_validate(user)
        return None

    @staticmethod
    async def get_by_id(db: AsyncSession, user_id: int) -> Optional[User]:
        """
        通过ID获取用户

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        result = await db.execute(select(DBUser).filter(DBUser.id == user_id))
        user = result.scalars().first()
        if user:
            return User.model_validate(user)
        return None

    @staticmethod
    async def create(db: AsyncSession, user_in: UserCreate) -> User:
        """
        创建用户

        Args:
            db: 数据库会话
            user_in: 用户创建模型

        Returns:
            User: 创建的用户对象
        """
        # 检查用户名是否已存在
        if await UserService.get_by_username(db, user_in.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在",
            )

        # 检查邮箱是否已存在
        if user_in.email and await UserService.get_by_email(db, user_in.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在",
            )

        # 创建用户
        db_user = DBUser(
            username=user_in.username,
            nickname=user_in.nickname,
            email=user_in.email,
            avatar=user_in.avatar,
            password=get_password_hash(user_in.password),
            is_active=user_in.is_active,
            is_superuser=user_in.is_superuser,
        )
        db.add(db_user)
        await db.commit()
        await db.refresh(db_user)

        return User.model_validate(db_user)

    @staticmethod
    async def authenticate(db: AsyncSession, username: str, password: str) -> Optional[User]:
        """
        认证用户

        Args:
            db: 数据库会话
            username: 用户名
            password: 密码

        Returns:
            Optional[User]: 认证成功的用户对象，如果认证失败则返回None
        """
        result = await db.execute(select(DBUser).filter(DBUser.username == username))
        user = result.scalars().first()
        if not user:
            return None
        if not user.is_active:
            return None
        if not verify_password(password, user.password):
            return None
        return User.model_validate(user)

    @staticmethod
    def create_token(user: User) -> Token:
        """
        创建用户令牌

        Args:
            user: 用户对象

        Returns:
            Token: 令牌对象
        """
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(subject=user.id, expires_delta=access_token_expires)
        return Token(access_token=access_token, token_type="Token")

    @staticmethod
    async def create_superuser(db: AsyncSession, username: str, email: EmailStr, password: str) -> Optional[User]:
        """
        创建超级管理员用户

        Args:
            db: 数据库会话
            username: 用户名
            email: 邮箱
            password: 密码

        Returns:
            Optional[User]: 创建的超级管理员用户对象，如果创建失败则返回None
        """
        user_in = UserCreate(
            username=username,
            nickname=None,  # 将使用默认昵称
            email=email,
            avatar=None,
            password=password,
            is_active=True,
            is_superuser=True,
        )
        return await UserService.create(db, user_in)

    @staticmethod
    async def get_list(db: AsyncSession, page_params: PageParams, query_params: UserListParams) -> PageResponse[User]:
        """
        获取用户列表

        Args:
            db: 数据库会话
            page_params: 分页参数
            query_params: 查询参数

        Returns:
            PageResponse[User]: 分页的用户列表
        """
        query = select(DBUser)

        # 机构过滤
        if query_params.organization_id:
            query = query.join(DBUserOrganization).filter(
                DBUserOrganization.organization_id == query_params.organization_id
            )

        # 激活状态过滤
        if query_params.is_active is not None:
            query = query.filter(DBUser.is_active == query_params.is_active)

        # 搜索过滤
        if query_params.search:
            search_filter = or_(
                DBUser.username.contains(query_params.search),
                DBUser.nickname.contains(query_params.search),
                DBUser.email.contains(query_params.search),
            )
            query = query.filter(search_filter)

        # 排序
        query = query.order_by(DBUser.created_at.desc())

        return await paginate_query(db, query, page_params, User)

    @staticmethod
    async def assign_to_organization(db: AsyncSession, assign_data: UserOrganizationAssign) -> bool:
        """
        将用户分配到机构

        Args:
            db: 数据库会话
            assign_data: 用户机构分配数据

        Returns:
            bool: 分配是否成功
        """
        # 检查是否已存在关联
        result = await db.execute(
            select(DBUserOrganization).filter(
                DBUserOrganization.user_id == assign_data.user_id,
                DBUserOrganization.organization_id == assign_data.organization_id,
            )
        )
        existing = result.scalars().first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户已在该机构中",
            )

        # 创建用户机构关联
        user_org = DBUserOrganization(
            user_id=assign_data.user_id,
            organization_id=assign_data.organization_id,
            role_id=assign_data.role_id,
            is_active=True,
        )
        db.add(user_org)
        await db.commit()

        return True

    @staticmethod
    async def remove_from_organization(db: AsyncSession, user_id: int, organization_id: int) -> bool:
        """
        将用户从机构中移除

        Args:
            db: 数据库会话
            user_id: 用户ID
            organization_id: 机构ID

        Returns:
            bool: 移除是否成功
        """
        result = await db.execute(
            select(DBUserOrganization).filter(
                DBUserOrganization.user_id == user_id, DBUserOrganization.organization_id == organization_id
            )
        )
        user_org = result.scalars().first()
        if not user_org:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户机构关联不存在",
            )

        await db.delete(user_org)
        await db.commit()

        return True

    @staticmethod
    async def update_organization_role(
        db: AsyncSession, user_id: int, organization_id: int, update_data: UserOrganizationUpdate
    ) -> bool:
        """
        更新用户在机构中的角色

        Args:
            db: 数据库会话
            user_id: 用户ID
            organization_id: 机构ID
            update_data: 更新数据

        Returns:
            bool: 更新是否成功
        """
        result = await db.execute(
            select(DBUserOrganization).filter(
                DBUserOrganization.user_id == user_id, DBUserOrganization.organization_id == organization_id
            )
        )
        user_org = result.scalars().first()
        if not user_org:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户机构关联不存在",
            )

        # 更新字段
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(user_org, field, value)

        await db.commit()

        return True
