from datetime import <PERSON><PERSON><PERSON>
from typing import Optional

from fastapi import HTTPException, status
from pydantic import EmailStr
from sqlalchemy import func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import (
    ACCESS_TOKEN_EXPIRE_MINUTES,
    create_access_token,
    get_password_hash,
    verify_password,
)
from app.models.system.user import User as DBUser
from app.modules.system.user.schemas import Token, User, UserCreate, UserListParams
from app.core.pagination import PageParams, PageResponse, paginate_query


class UserService:
    """用户服务类"""

    @staticmethod
    async def get_by_username(db: AsyncSession, username: str) -> Optional[User]:
        """
        通过用户名获取用户

        Args:
            db: 数据库会话
            username: 用户名

        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        result = await db.execute(select(DBUser).filter(DBUser.username == username))
        user = result.scalars().first()
        if user:
            return User.model_validate(user)
        return None

    @staticmethod
    async def get_by_email(db: AsyncSession, email: EmailStr) -> Optional[User]:
        """
        通过邮箱获取用户

        Args:
            db: 数据库会话
            email: 邮箱

        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        if not email:
            return None
        result = await db.execute(select(DBUser).filter(DBUser.email == email))
        user = result.scalars().first()
        if user:
            return User.model_validate(user)
        return None

    @staticmethod
    async def get_by_id(db: AsyncSession, user_id: int) -> Optional[User]:
        """
        通过ID获取用户

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        result = await db.execute(select(DBUser).filter(DBUser.id == user_id))
        user = result.scalars().first()
        if user:
            return User.model_validate(user)
        return None

    @staticmethod
    async def create(db: AsyncSession, user_in: UserCreate) -> User:
        """
        创建用户

        Args:
            db: 数据库会话
            user_in: 用户创建模型

        Returns:
            User: 创建的用户对象
        """
        # 检查用户名是否已存在
        if await UserService.get_by_username(db, user_in.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在",
            )

        # 检查邮箱是否已存在
        if user_in.email and await UserService.get_by_email(db, user_in.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在",
            )

        # 创建用户
        db_user = DBUser(
            username=user_in.username,
            email=user_in.email,
            password=get_password_hash(user_in.password),
            is_active=user_in.is_active,
        )
        db.add(db_user)
        await db.commit()
        await db.refresh(db_user)

        return User.model_validate(db_user)

    @staticmethod
    async def authenticate(db: AsyncSession, username: str, password: str) -> Optional[User]:
        """
        认证用户

        Args:
            db: 数据库会话
            username: 用户名
            password: 密码

        Returns:
            Optional[User]: 认证成功的用户对象，如果认证失败则返回None
        """
        result = await db.execute(select(DBUser).filter(DBUser.username == username))
        user = result.scalars().first()
        if not user:
            return None
        if not user.is_active:
            return None
        if not verify_password(password, user.password):
            return None
        return User.model_validate(user)

    @staticmethod
    def create_token(user: User) -> Token:
        """
        创建用户令牌

        Args:
            user: 用户对象

        Returns:
            Token: 令牌对象
        """
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(subject=user.id, expires_delta=access_token_expires)
        return Token(access_token=access_token, token_type="Token")

    @staticmethod
    async def create_superuser(db: AsyncSession, username: str, email: EmailStr, password: str) -> Optional[User]:
        """
        创建超级管理员用户

        Args:
            db: 数据库会话
            username: 用户名
            email: 邮箱
            password: 密码

        Returns:
            Optional[User]: 创建的超级管理员用户对象，如果创建失败则返回None
        """
        user_in = UserCreate(
            username=username,
            email=email,
            password=password,
            is_active=True,
            is_superuser=True,
        )
        return await UserService.create(db, user_in)

    @staticmethod
    async def get_list(db: AsyncSession, page_params: PageParams, query_params: UserListParams) -> PageResponse[User]:
        """
        获取用户列表

        Args:
            db: 数据库会话
            page_params: 分页参数
            query_params: 查询参数

        Returns:
            PageResponse[User]: 分页的用户列表
        """
        # 构建基础查询
        query = select(DBUser)

        # 添加搜索条件
        if query_params.search:
            search_term = f"%{query_params.search}%"
            query = query.filter(or_(DBUser.username.ilike(search_term), DBUser.email.ilike(search_term)))

        # 按创建时间倒序排列
        query = query.order_by(DBUser.created_at.desc())

        # 构建计数查询
        count_query = select(func.count(DBUser.id))
        if query_params.search:
            search_term = f"%{query_params.search}%"
            count_query = count_query.filter(or_(DBUser.username.ilike(search_term), DBUser.email.ilike(search_term)))

        # 执行分页查询
        db_users, total = await paginate_query(db, query, page_params, count_query)

        # 转换为Pydantic模型
        users = [User.model_validate(user) for user in db_users]

        return PageResponse.create(users, total, page_params)
