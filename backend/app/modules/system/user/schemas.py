from datetime import datetime
from typing import Optional, List
import random
import string

from fastapi import Query
from pydantic import BaseModel, EmailStr, Field, model_validator


class UserBase(BaseModel):
    """用户基础模型"""

    username: str = Field(..., description="用户名", min_length=1, max_length=50)
    nickname: Optional[str] = Field(None, description="昵称", max_length=100)
    email: Optional[EmailStr] = Field(None, description="邮箱")
    avatar: Optional[str] = Field(None, description="头像URL")
    is_active: bool = Field(True, description="是否激活")
    is_superuser: bool = Field(False, description="是否为超级管理员")

    @model_validator(mode="before")
    @classmethod
    def set_default_nickname(cls, values):
        """设置默认昵称：用户+随机字符"""
        if isinstance(values, dict) and not values.get("nickname") and values.get("username"):
            random_suffix = "".join(random.choices(string.ascii_lowercase + string.digits, k=6))
            values["nickname"] = f"用户{random_suffix}"
        return values


class UserCreate(UserBase):
    """用户创建模型"""

    password: str = Field(..., description="密码")


class UserUpdate(UserBase):
    """用户更新模型"""

    password: Optional[str] = Field(None, description="密码")


class User(UserBase):
    """返回给API的用户模型"""

    id: int = Field(..., description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """用户登录模型"""

    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class Token(BaseModel):
    """令牌模型"""

    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(..., description="令牌类型")


class TokenPayload(BaseModel):
    """令牌载荷模型"""

    sub: Optional[str] = Field(None, description="主题（用户ID字符串）")
    exp: Optional[datetime] = Field(None, description="过期时间")


class UserOrganizationAssign(BaseModel):
    """用户机构分配模型"""

    user_id: int = Field(..., description="用户ID")
    organization_id: int = Field(..., description="机构ID")
    role_id: Optional[int] = Field(None, description="角色ID")


class UserOrganizationUpdate(BaseModel):
    """用户机构关系更新模型"""

    role_id: Optional[int] = Field(None, description="角色ID")
    is_active: Optional[bool] = Field(None, description="在该机构下是否激活")


class UserWithOrganizations(User):
    """包含机构信息的用户模型"""

    organizations: List["UserOrganizationInfo"] = Field([], description="用户机构列表")


class UserOrganizationInfo(BaseModel):
    """用户机构信息"""

    organization_id: int = Field(..., description="机构ID")
    organization_name: str = Field(..., description="机构名称")
    organization_code: str = Field(..., description="机构标识")
    role_id: Optional[int] = Field(None, description="角色ID")
    role_name: Optional[str] = Field(None, description="角色名称")
    is_active: bool = Field(..., description="在该机构下是否激活")

    class Config:
        from_attributes = True


class UserListParams(BaseModel):
    """用户列表查询参数模型"""

    search: Optional[str] = Field(None, description="搜索关键词，支持用户名、昵称和邮箱搜索")
    organization_id: Optional[int] = Field(None, description="机构ID")
    is_active: Optional[bool] = Field(None, description="是否激活")


def get_user_list_params(
    search: Optional[str] = Query(None, description="搜索关键词，支持用户名、昵称和邮箱搜索"),
    organization_id: Optional[int] = Query(None, description="机构ID"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
) -> UserListParams:
    """获取用户列表查询参数的依赖函数"""
    return UserListParams(
        search=search,
        organization_id=organization_id,
        is_active=is_active,
    )


# 解决前向引用问题
UserWithOrganizations.model_rebuild()
