from typing import Optional, List

from fastapi import HTTPException, status
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.system.role import Role as DBRole
from app.models.system.permission import Permission as DBPermission
from app.models.system.role_permission import RolePermission as DBRolePermission
from app.models.system.user_organization import UserOrganization as DBUserOrganization
from app.modules.system.role.schemas import Role, RoleCreate, RoleUpdate, RoleListParams, RoleWithPermissions
from app.modules.system.permission.schemas import Permission
from app.core.pagination import PageParams, PageResponse, paginate_query


class RoleService:
    """角色服务类"""

    @staticmethod
    async def get_by_id(db: AsyncSession, role_id: int) -> Optional[Role]:
        """
        根据ID获取角色

        Args:
            db: 数据库会话
            role_id: 角色ID

        Returns:
            Optional[Role]: 角色对象，如果不存在则返回None
        """
        result = await db.execute(select(DBRole).filter(DBRole.id == role_id))
        role = result.scalars().first()
        if role:
            return Role.model_validate(role)
        return None

    @staticmethod
    async def get_by_id_with_permissions(db: AsyncSession, role_id: int) -> Optional[RoleWithPermissions]:
        """
        根据ID获取角色及其权限

        Args:
            db: 数据库会话
            role_id: 角色ID

        Returns:
            Optional[RoleWithPermissions]: 包含权限的角色对象，如果不存在则返回None
        """
        result = await db.execute(
            select(DBRole)
            .options(selectinload(DBRole.role_permissions).selectinload(DBRolePermission.permission))
            .filter(DBRole.id == role_id)
        )
        role = result.scalars().first()
        if role:
            permissions = [Permission.model_validate(rp.permission) for rp in role.role_permissions]
            role_data = Role.model_validate(role)
            return RoleWithPermissions(**role_data.model_dump(), permissions=permissions)
        return None

    @staticmethod
    async def get_list(
        db: AsyncSession, page_params: PageParams, query_params: RoleListParams
    ) -> PageResponse[Role]:
        """
        获取角色列表

        Args:
            db: 数据库会话
            page_params: 分页参数
            query_params: 查询参数

        Returns:
            PageResponse[Role]: 分页的角色列表
        """
        query = select(DBRole)

        # 机构过滤
        if query_params.organization_id:
            query = query.filter(DBRole.organization_id == query_params.organization_id)

        # 搜索过滤
        if query_params.search:
            query = query.filter(DBRole.name.contains(query_params.search))

        # 排序
        query = query.order_by(DBRole.is_admin.desc(), DBRole.created_at.desc())

        return await paginate_query(db, query, page_params, Role)

    @staticmethod
    async def create(db: AsyncSession, role_in: RoleCreate) -> Role:
        """
        创建角色

        Args:
            db: 数据库会话
            role_in: 角色创建模型

        Returns:
            Role: 创建的角色对象
        """
        # 检查角色名在机构内是否已存在
        result = await db.execute(
            select(DBRole).filter(
                DBRole.name == role_in.name,
                DBRole.organization_id == role_in.organization_id
            )
        )
        existing_role = result.scalars().first()
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该机构内角色名已存在",
            )

        # 创建角色
        db_role = DBRole(
            name=role_in.name,
            description=role_in.description,
            organization_id=role_in.organization_id,
            is_admin=False,  # 普通角色不是管理员
        )
        db.add(db_role)
        await db.commit()
        await db.refresh(db_role)

        # 分配权限
        if role_in.permission_ids:
            await RoleService._assign_permissions(db, db_role.id, role_in.permission_ids)

        return Role.model_validate(db_role)

    @staticmethod
    async def update(db: AsyncSession, role_id: int, role_in: RoleUpdate) -> Role:
        """
        更新角色

        Args:
            db: 数据库会话
            role_id: 角色ID
            role_in: 角色更新模型

        Returns:
            Role: 更新后的角色对象
        """
        result = await db.execute(select(DBRole).filter(DBRole.id == role_id))
        db_role = result.scalars().first()
        if not db_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="角色不存在",
            )

        # 检查是否为机构管理员角色
        if db_role.is_admin:
            # 机构管理员角色不可编辑权限，只能编辑名称和描述
            if role_in.permission_ids is not None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="机构管理员角色不可编辑权限",
                )

        # 检查角色名在机构内是否已存在
        if role_in.name and role_in.name != db_role.name:
            result = await db.execute(
                select(DBRole).filter(
                    DBRole.name == role_in.name,
                    DBRole.organization_id == db_role.organization_id,
                    DBRole.id != role_id
                )
            )
            existing_role = result.scalars().first()
            if existing_role:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="该机构内角色名已存在",
                )

        # 更新基本字段
        update_data = role_in.model_dump(exclude_unset=True, exclude={"permission_ids"})
        for field, value in update_data.items():
            setattr(db_role, field, value)

        await db.commit()
        await db.refresh(db_role)

        # 更新权限
        if role_in.permission_ids is not None and not db_role.is_admin:
            await RoleService._assign_permissions(db, role_id, role_in.permission_ids)

        return Role.model_validate(db_role)

    @staticmethod
    async def delete(db: AsyncSession, role_id: int) -> bool:
        """
        删除角色

        Args:
            db: 数据库会话
            role_id: 角色ID

        Returns:
            bool: 删除是否成功
        """
        result = await db.execute(select(DBRole).filter(DBRole.id == role_id))
        db_role = result.scalars().first()
        if not db_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="角色不存在",
            )

        # 检查是否为机构管理员角色
        if db_role.is_admin:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="机构管理员角色不可删除",
            )

        # 检查是否有用户绑定该角色
        user_count_result = await db.execute(
            select(func.count(DBUserOrganization.id)).filter(DBUserOrganization.role_id == role_id)
        )
        user_count = user_count_result.scalar()
        if user_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色下存在用户，请先解绑用户",
            )

        # 删除角色
        await db.delete(db_role)
        await db.commit()

        return True

    @staticmethod
    async def _assign_permissions(db: AsyncSession, role_id: int, permission_ids: List[int]) -> None:
        """
        为角色分配权限

        Args:
            db: 数据库会话
            role_id: 角色ID
            permission_ids: 权限ID列表
        """
        # 删除现有权限
        await db.execute(
            select(DBRolePermission).filter(DBRolePermission.role_id == role_id)
        )
        result = await db.execute(
            select(DBRolePermission).filter(DBRolePermission.role_id == role_id)
        )
        existing_permissions = result.scalars().all()
        for rp in existing_permissions:
            await db.delete(rp)

        # 添加新权限
        for permission_id in permission_ids:
            role_permission = DBRolePermission(
                role_id=role_id,
                permission_id=permission_id
            )
            db.add(role_permission)

        await db.commit()

    @staticmethod
    async def get_role_permissions(db: AsyncSession, role_id: int) -> List[Permission]:
        """
        获取角色的权限列表

        Args:
            db: 数据库会话
            role_id: 角色ID

        Returns:
            List[Permission]: 权限列表
        """
        result = await db.execute(
            select(DBPermission)
            .join(DBRolePermission, DBPermission.id == DBRolePermission.permission_id)
            .filter(DBRolePermission.role_id == role_id)
            .order_by(DBPermission.module, DBPermission.code)
        )
        permissions = result.scalars().all()
        return [Permission.model_validate(permission) for permission in permissions]
