from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.modules.system.role.schemas import (
    Role,
    RoleCreate,
    RoleUpdate,
    RoleListParams,
    RoleWithPermissions,
    get_role_list_params,
)
from app.modules.system.role.service import RoleService
from app.modules.system.permission.schemas import Permission
from app.core.pagination import PageResponse, get_page_params, PageParams
from app.modules.system.user.deps import get_current_user
from app.modules.system.user.schemas import User

router = APIRouter()


@router.post("/", response_model=Role, summary="创建角色")
async def create_role(
    params: RoleCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    创建角色接口

    - name: 角色名称（机构内唯一）
    - description: 角色简介（可选）
    - organization_id: 所属机构ID
    - permission_ids: 权限ID列表（可选）

    只有机构管理员可以访问
    """
    # TODO: 添加机构管理员权限检查
    return await RoleService.create(db, params)


@router.get("/", response_model=PageResponse[Role], summary="获取角色列表")
async def get_role_list(
    db: AsyncSession = Depends(get_db),
    page_params: PageParams = Depends(get_page_params),
    query_params: RoleListParams = Depends(get_role_list_params),
    current_user: User = Depends(get_current_user),
):
    """
    获取角色列表接口

    支持分页和搜索功能：
    - page: 页码，从1开始
    - size: 每页数量，最大100
    - search: 搜索关键词，支持角色名称搜索
    - organization_id: 机构ID

    只有机构管理员可以访问
    """
    # TODO: 添加机构管理员权限检查和机构过滤
    return await RoleService.get_list(db, page_params, query_params)


@router.get("/{role_id}", response_model=RoleWithPermissions, summary="获取角色详情")
async def get_role(
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取角色详情接口

    只有机构管理员可以访问
    """
    # TODO: 添加机构管理员权限检查
    
    role = await RoleService.get_by_id_with_permissions(db, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在",
        )
    return role


@router.put("/{role_id}", response_model=Role, summary="更新角色")
async def update_role(
    role_id: int,
    params: RoleUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    更新角色接口

    只有机构管理员可以访问
    保护机制：机构管理员角色不可编辑权限
    """
    # TODO: 添加机构管理员权限检查
    return await RoleService.update(db, role_id, params)


@router.delete("/{role_id}", summary="删除角色")
async def delete_role(
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    删除角色接口

    前置检查：需解绑用户
    保护机制：机构管理员角色不可删除

    只有机构管理员可以访问
    """
    # TODO: 添加机构管理员权限检查
    
    await RoleService.delete(db, role_id)
    return {"message": "角色删除成功"}


@router.get("/{role_id}/permissions", response_model=List[Permission], summary="获取角色权限")
async def get_role_permissions(
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取角色权限列表接口

    只有机构管理员可以访问
    """
    # TODO: 添加机构管理员权限检查
    
    # 检查角色是否存在
    role = await RoleService.get_by_id(db, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在",
        )
    
    return await RoleService.get_role_permissions(db, role_id)
