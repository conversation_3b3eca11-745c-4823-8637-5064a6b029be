from typing import Optional, List

from fastapi import HTTPException, status
from sqlalchemy import or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.system.permission import Permission as DBPermission
from app.modules.system.permission.schemas import Permission, PermissionCreate, PermissionUpdate, PermissionListParams
from app.core.pagination import PageParams, PageResponse, paginate_query


class PermissionService:
    """权限服务类"""

    @staticmethod
    async def get_by_id(db: AsyncSession, permission_id: int) -> Optional[Permission]:
        """
        根据ID获取权限

        Args:
            db: 数据库会话
            permission_id: 权限ID

        Returns:
            Optional[Permission]: 权限对象，如果不存在则返回None
        """
        result = await db.execute(select(DBPermission).filter(DBPermission.id == permission_id))
        permission = result.scalars().first()
        if permission:
            return Permission.model_validate(permission)
        return None

    @staticmethod
    async def get_by_code(db: AsyncSession, code: str) -> Optional[Permission]:
        """
        根据权限码获取权限

        Args:
            db: 数据库会话
            code: 权限码

        Returns:
            Optional[Permission]: 权限对象，如果不存在则返回None
        """
        result = await db.execute(select(DBPermission).filter(DBPermission.code == code))
        permission = result.scalars().first()
        if permission:
            return Permission.model_validate(permission)
        return None

    @staticmethod
    async def get_list(
        db: AsyncSession, page_params: PageParams, query_params: PermissionListParams
    ) -> PageResponse[Permission]:
        """
        获取权限列表

        Args:
            db: 数据库会话
            page_params: 分页参数
            query_params: 查询参数

        Returns:
            PageResponse[Permission]: 分页的权限列表
        """
        query = select(DBPermission)

        # 搜索过滤
        if query_params.search:
            search_filter = or_(
                DBPermission.name.contains(query_params.search),
                DBPermission.code.contains(query_params.search),
            )
            query = query.filter(search_filter)

        # 模块过滤
        if query_params.module:
            query = query.filter(DBPermission.module == query_params.module)

        # 排序
        query = query.order_by(DBPermission.module, DBPermission.code)

        return await paginate_query(db, query, page_params, Permission)

    @staticmethod
    async def get_all(db: AsyncSession) -> List[Permission]:
        """
        获取所有权限

        Args:
            db: 数据库会话

        Returns:
            List[Permission]: 权限列表
        """
        result = await db.execute(select(DBPermission).order_by(DBPermission.module, DBPermission.code))
        permissions = result.scalars().all()
        return [Permission.model_validate(permission) for permission in permissions]

    @staticmethod
    async def get_modules(db: AsyncSession) -> List[str]:
        """
        获取所有模块名称

        Args:
            db: 数据库会话

        Returns:
            List[str]: 模块名称列表
        """
        result = await db.execute(select(DBPermission.module).distinct().order_by(DBPermission.module))
        modules = result.scalars().all()
        return list(modules)

    @staticmethod
    async def create(db: AsyncSession, permission_in: PermissionCreate) -> Permission:
        """
        创建权限

        Args:
            db: 数据库会话
            permission_in: 权限创建模型

        Returns:
            Permission: 创建的权限对象
        """
        # 检查权限码是否已存在
        if await PermissionService.get_by_code(db, permission_in.code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="权限码已存在",
            )

        # 创建权限
        db_permission = DBPermission(
            name=permission_in.name,
            code=permission_in.code,
            module=permission_in.module,
            description=permission_in.description,
        )
        db.add(db_permission)
        await db.commit()
        await db.refresh(db_permission)

        return Permission.model_validate(db_permission)

    @staticmethod
    async def update(db: AsyncSession, permission_id: int, permission_in: PermissionUpdate) -> Permission:
        """
        更新权限

        Args:
            db: 数据库会话
            permission_id: 权限ID
            permission_in: 权限更新模型

        Returns:
            Permission: 更新后的权限对象
        """
        result = await db.execute(select(DBPermission).filter(DBPermission.id == permission_id))
        db_permission = result.scalars().first()
        if not db_permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="权限不存在",
            )

        # 更新字段
        update_data = permission_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_permission, field, value)

        await db.commit()
        await db.refresh(db_permission)

        return Permission.model_validate(db_permission)

    @staticmethod
    async def delete(db: AsyncSession, permission_id: int) -> bool:
        """
        删除权限

        Args:
            db: 数据库会话
            permission_id: 权限ID

        Returns:
            bool: 删除是否成功
        """
        result = await db.execute(select(DBPermission).filter(DBPermission.id == permission_id))
        db_permission = result.scalars().first()
        if not db_permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="权限不存在",
            )

        # 删除权限
        await db.delete(db_permission)
        await db.commit()

        return True

    @staticmethod
    async def batch_create(db: AsyncSession, permissions: List[PermissionCreate]) -> List[Permission]:
        """
        批量创建权限

        Args:
            db: 数据库会话
            permissions: 权限创建模型列表

        Returns:
            List[Permission]: 创建的权限对象列表
        """
        created_permissions = []
        for permission_in in permissions:
            # 检查权限码是否已存在
            existing = await PermissionService.get_by_code(db, permission_in.code)
            if not existing:
                db_permission = DBPermission(
                    name=permission_in.name,
                    code=permission_in.code,
                    module=permission_in.module,
                    description=permission_in.description,
                )
                db.add(db_permission)
                created_permissions.append(db_permission)

        await db.commit()
        
        # 刷新所有创建的权限
        for permission in created_permissions:
            await db.refresh(permission)

        return [Permission.model_validate(permission) for permission in created_permissions]
