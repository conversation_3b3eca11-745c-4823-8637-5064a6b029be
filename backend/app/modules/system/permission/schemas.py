from datetime import datetime
from typing import Optional

from fastapi import Query
from pydantic import BaseModel, Field


class PermissionBase(BaseModel):
    """权限基础模型"""

    name: str = Field(..., description="权限名称", min_length=1, max_length=100)
    code: str = Field(..., description="权限码", min_length=1, max_length=100)
    module: str = Field(..., description="所属模块", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="权限描述")


class PermissionCreate(PermissionBase):
    """权限创建模型"""
    pass


class PermissionUpdate(BaseModel):
    """权限更新模型"""

    name: Optional[str] = Field(None, description="权限名称", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="权限描述")


class Permission(PermissionBase):
    """返回给API的权限模型"""

    id: int = Field(..., description="权限ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class PermissionListParams(BaseModel):
    """权限列表查询参数"""

    search: Optional[str] = Field(None, description="搜索关键词，支持权限名称和权限码搜索")
    module: Optional[str] = Field(None, description="模块名称")


def get_permission_list_params(
    search: Optional[str] = Query(None, description="搜索关键词，支持权限名称和权限码搜索"),
    module: Optional[str] = Query(None, description="模块名称"),
) -> PermissionListParams:
    """获取权限列表查询参数"""
    return PermissionListParams(search=search, module=module)
