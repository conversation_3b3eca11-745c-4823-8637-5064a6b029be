from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.modules.system.permission.schemas import (
    Permission,
    PermissionCreate,
    PermissionUpdate,
    PermissionListParams,
    get_permission_list_params,
)
from app.modules.system.permission.service import PermissionService
from app.core.pagination import PageResponse, get_page_params, PageParams
from app.modules.system.user.deps import get_current_user
from app.modules.system.user.schemas import User

router = APIRouter()


@router.post("/", response_model=Permission, summary="创建权限")
async def create_permission(
    params: PermissionCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    创建权限接口

    - name: 权限名称
    - code: 权限码（唯一）
    - module: 所属模块
    - description: 权限描述（可选）

    只有超级管理员可以访问
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以创建权限",
        )
    return await PermissionService.create(db, params)


@router.get("/", response_model=PageResponse[Permission], summary="获取权限列表")
async def get_permission_list(
    db: AsyncSession = Depends(get_db),
    page_params: PageParams = Depends(get_page_params),
    query_params: PermissionListParams = Depends(get_permission_list_params),
    current_user: User = Depends(get_current_user),
):
    """
    获取权限列表接口

    支持分页和搜索功能：
    - page: 页码，从1开始
    - size: 每页数量，最大100
    - search: 搜索关键词，支持权限名称和权限码搜索
    - module: 模块名称

    机构管理员根据所属机构可见模块，获取权限列表
    """
    # TODO: 添加机构权限过滤
    return await PermissionService.get_list(db, page_params, query_params)


@router.get("/all", response_model=List[Permission], summary="获取所有权限")
async def get_all_permissions(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取所有权限接口

    机构管理员根据所属机构可见模块，获取权限列表
    """
    # TODO: 添加机构权限过滤
    return await PermissionService.get_all(db)


@router.get("/modules", response_model=List[str], summary="获取所有模块")
async def get_modules(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取所有模块名称接口

    超级管理员可维护机构可见的模块
    机构管理员根据所属机构可见模块，获取模块列表
    """
    # TODO: 添加机构权限过滤
    return await PermissionService.get_modules(db)


@router.get("/{permission_id}", response_model=Permission, summary="获取权限详情")
async def get_permission(
    permission_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取权限详情接口
    """
    permission = await PermissionService.get_by_id(db, permission_id)
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在",
        )
    return permission


@router.put("/{permission_id}", response_model=Permission, summary="更新权限")
async def update_permission(
    permission_id: int,
    params: PermissionUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    更新权限接口

    权限本身不可编辑（只能编辑名称和描述）

    只有超级管理员可以访问
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以更新权限",
        )
    return await PermissionService.update(db, permission_id, params)


@router.delete("/{permission_id}", summary="删除权限")
async def delete_permission(
    permission_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    删除权限接口

    只有超级管理员可以访问
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以删除权限",
        )
    
    await PermissionService.delete(db, permission_id)
    return {"message": "权限删除成功"}


@router.post("/batch", response_model=List[Permission], summary="批量创建权限")
async def batch_create_permissions(
    permissions: List[PermissionCreate],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    批量创建权限接口

    只有超级管理员可以访问
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以批量创建权限",
        )
    return await PermissionService.batch_create(db, permissions)
