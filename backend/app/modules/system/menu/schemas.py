from datetime import datetime
from typing import Optional, List, Dict, Any

from fastapi import Query
from pydantic import BaseModel, Field


class MenuBase(BaseModel):
    """菜单基础模型"""

    name: str = Field(..., description="菜单名称", min_length=1, max_length=100)
    path: Optional[str] = Field(None, description="路由路径", max_length=255)
    component: Optional[str] = Field(None, description="组件路径", max_length=255)
    permission_id: Optional[int] = Field(None, description="关联权限ID")
    parent_id: Optional[int] = Field(None, description="父菜单ID")
    target: str = Field("_self", description="打开方式")
    is_hidden: bool = Field(False, description="是否隐藏")
    sort_order: int = Field(0, description="排序")
    icon: Optional[str] = Field(None, description="图标", max_length=100)
    keepalive: bool = Field(False, description="是否缓存")
    redirect: Optional[str] = Field(None, description="重定向路径", max_length=255)
    meta: Optional[Dict[str, Any]] = Field(None, description="其他前端配置")


class MenuCreate(MenuBase):
    """菜单创建模型"""

    organization_id: int = Field(..., description="所属机构ID")


class MenuUpdate(BaseModel):
    """菜单更新模型"""

    name: Optional[str] = Field(None, description="菜单名称", min_length=1, max_length=100)
    path: Optional[str] = Field(None, description="路由路径", max_length=255)
    component: Optional[str] = Field(None, description="组件路径", max_length=255)
    permission_id: Optional[int] = Field(None, description="关联权限ID")
    parent_id: Optional[int] = Field(None, description="父菜单ID")
    target: Optional[str] = Field(None, description="打开方式")
    is_hidden: Optional[bool] = Field(None, description="是否隐藏")
    sort_order: Optional[int] = Field(None, description="排序")
    icon: Optional[str] = Field(None, description="图标", max_length=100)
    keepalive: Optional[bool] = Field(None, description="是否缓存")
    redirect: Optional[str] = Field(None, description="重定向路径", max_length=255)
    meta: Optional[Dict[str, Any]] = Field(None, description="其他前端配置")


class Menu(MenuBase):
    """返回给API的菜单模型"""

    id: int = Field(..., description="菜单ID")
    organization_id: int = Field(..., description="所属机构ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class MenuTree(Menu):
    """菜单树形结构模型"""

    children: List["MenuTree"] = Field([], description="子菜单列表")


class MenuListParams(BaseModel):
    """菜单列表查询参数"""

    search: Optional[str] = Field(None, description="搜索关键词，支持菜单名称搜索")
    organization_id: Optional[int] = Field(None, description="机构ID")
    parent_id: Optional[int] = Field(None, description="父菜单ID")


def get_menu_list_params(
    search: Optional[str] = Query(None, description="搜索关键词，支持菜单名称搜索"),
    organization_id: Optional[int] = Query(None, description="机构ID"),
    parent_id: Optional[int] = Query(None, description="父菜单ID"),
) -> MenuListParams:
    """获取菜单列表查询参数"""
    return MenuListParams(search=search, organization_id=organization_id, parent_id=parent_id)


# 解决前向引用问题
MenuTree.model_rebuild()
