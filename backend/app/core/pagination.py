from typing import Any, Generic, List, Optional, TypeVar
from math import ceil

from fastapi import Query
from pydantic import BaseModel, Field
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

T = TypeVar("T")


class PageParams(BaseModel):
    """分页参数模型"""

    page: int = Field(1, ge=1, description="页码，从1开始")
    size: int = Field(10, ge=1, le=100, description="每页数量，最大100")

    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.size


class PageResponse(BaseModel, Generic[T]):
    """分页响应模型"""

    results: List[T] = Field(..., description="数据列表")
    count: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    pages: int = Field(..., description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")

    @classmethod
    def create(cls, results: List[T], count: int, page_params: PageParams) -> "PageResponse[T]":
        """创建分页响应"""
        pages = ceil(count / page_params.size) if count > 0 else 0
        return cls(
            results=results,
            count=count,
            page=page_params.page,
            size=page_params.size,
            pages=pages,
            has_next=page_params.page < pages,
            has_prev=page_params.page > 1,
        )


def get_page_params(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
) -> PageParams:
    """获取分页参数的依赖函数"""
    return PageParams(page=page, size=size)


async def paginate_query(
    db: AsyncSession,
    query,
    page_params: PageParams,
    count_query: Optional[Any] = None,
) -> tuple[List, int]:
    """
    对查询进行分页处理

    Args:
        db: 数据库会话
        query: 查询对象
        page_params: 分页参数
        count_query: 计数查询，如果不提供则自动生成

    Returns:
        tuple[List, int]: (数据列表, 总数量)
    """
    # 获取总数量
    if count_query is None:
        count_query = select(func.count()).select_from(query.subquery())

    total_result = await db.execute(count_query)
    total = total_result.scalar() or 0

    # 获取分页数据
    paginated_query = query.offset(page_params.offset).limit(page_params.size)
    result = await db.execute(paginated_query)
    items = result.scalars().all()

    return items, total
