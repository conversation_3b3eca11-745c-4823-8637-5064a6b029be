from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from utils.config import settings
from app.modules.router import api_router


def register_middlewares(app: FastAPI) -> None:
    """
    注册中间件

    Args:
        app (FastAPI): FastAPI应用实例
    """
    # 跨域中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors.allow_origins,
        allow_credentials=settings.cors.allow_credentials,
        allow_methods=settings.cors.allow_methods,
        allow_headers=settings.cors.allow_headers,
    )


def register_exceptions(app: FastAPI):
    pass


def register_routers(app: FastAPI):
    """
    注册路由

    Args:
        app (FastAPI): FastAPI应用实例
        prefix (str): 路由前缀
    """
    app.include_router(api_router, prefix="/api")
