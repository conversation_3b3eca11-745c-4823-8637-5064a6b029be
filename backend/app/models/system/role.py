from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, Text, Integer, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Role(BaseModel):
    """角色数据库模型"""

    __tablename__ = "system_roles"

    name = Column(String(100), nullable=False, comment="角色名称")
    description = Column(Text, nullable=True, comment="角色简介")
    organization_id = Column(Integer, ForeignKey("system_organizations.id"), nullable=False, comment="所属机构ID")
    is_admin = Column(Boolean, default=False, comment="是否为机构管理员")
    
    # 关联关系
    organization = relationship("Organization", back_populates="roles")
    user_organizations = relationship("UserOrganization", back_populates="role")
    role_permissions = relationship("RolePermission", back_populates="role", cascade="all, delete-orphan")
    
    # 添加唯一约束：同一机构内角色名唯一
    __table_args__ = (
        {"comment": "角色表"},
    )
