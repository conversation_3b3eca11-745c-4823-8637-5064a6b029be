from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, String
from sqlalchemy.sql import func

from app.models.base import BaseModel


class User(BaseModel):
    """用户数据库模型"""

    __tablename__ = "system_users"

    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=True)
    password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, onupdate=func.now(), nullable=True)
