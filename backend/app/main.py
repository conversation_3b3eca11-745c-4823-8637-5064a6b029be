import logging

from utils.config import settings
from fastapi import FastAPI
from app.core.init_app import register_middlewares, register_exceptions, register_routers

logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    app = FastAPI(
        title=settings.app.title,
        description=settings.app.description,
        version=settings.app.version,
    )

    register_middlewares(app)
    register_exceptions(app)
    register_routers(app)

    return app


app = create_app()
