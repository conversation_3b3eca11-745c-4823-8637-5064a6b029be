version = 1

disable_existing_loggers = false

[formatters.standard]
format = "[%(asctime)s] [%(levelname)s] [%(name)s:%(lineno)s] %(message)s"

[handlers.console]
class = "rich.logging.RichHandler"
level = "INFO"
formatter = "standard"

[handlers.file]
class = "logging.handlers.TimedRotatingFileHandler"
level = "INFO"
formatter = "standard"
filename = "logs/app.log"
when = "midnight"
backupCount = 7

[root]
level = "INFO"
handlers = ["console", "file"]
