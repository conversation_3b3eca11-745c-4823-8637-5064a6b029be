[default.settings]
debug = false

[default.settings.app]
title = "zkm"
description = ""
version = "0.0.1"

[default.settings.security]
secret_key = "z<PERSON>hao"
algorithm = "HS256"
access_token_expire_minutes = 30

[default.settings.cors]
allow_origins = ["*"]
allow_credentials = true
allow_methods = ["*"]
allow_headers = ["*"]

[default.settings.database]
url = "sqlite:///./zkm.db"
async_url = "sqlite+aiosqlite:///./zkm.db"
pool_size = 5
max_overflow = 10
pool_timeout = 30
pool_recycle = 3600

[development.settings]
dynaconf_merge = true
debug = true

[development.settings.server]
host = "0.0.0.0"
port = 8000
workers = 1

[production.settings]
dynaconf_merge = true
debug = false
