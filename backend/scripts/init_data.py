import asyncio
import sys
from pathlib import Path

import typer
from utils.logging import get_logger

# 添加项目根目录到 Python 路径
sys.path.append(str(Path(__file__).parent.parent))

from app.core.database import AsyncSessionLocal
from app.modules.system.organization.service import OrganizationService
from app.modules.system.permission.service import PermissionService
from app.modules.system.organization.schemas import OrganizationCreate
from app.modules.system.permission.schemas import PermissionCreate

logger = get_logger(__name__)

# 创建 Typer 应用
app = typer.Typer(help="初始化数据工具")

# 默认权限配置
DEFAULT_PERMISSIONS = [
    # 系统管理
    {"name": "系统管理", "code": "/system", "module": "system", "description": "系统管理模块"},
    
    # 用户管理
    {"name": "用户管理", "code": "/system/user", "module": "system", "description": "用户管理模块"},
    {"name": "查看用户", "code": "/system/user/read", "module": "system", "description": "查看用户列表和详情"},
    {"name": "创建用户", "code": "/system/user/create", "module": "system", "description": "创建新用户"},
    {"name": "编辑用户", "code": "/system/user/edit", "module": "system", "description": "编辑用户信息"},
    {"name": "删除用户", "code": "/system/user/delete", "module": "system", "description": "删除用户"},
    
    # 机构管理
    {"name": "机构管理", "code": "/system/organization", "module": "system", "description": "机构管理模块"},
    {"name": "查看机构", "code": "/system/organization/read", "module": "system", "description": "查看机构列表和详情"},
    {"name": "创建机构", "code": "/system/organization/create", "module": "system", "description": "创建新机构"},
    {"name": "编辑机构", "code": "/system/organization/edit", "module": "system", "description": "编辑机构信息"},
    {"name": "删除机构", "code": "/system/organization/delete", "module": "system", "description": "删除机构"},
    
    # 角色管理
    {"name": "角色管理", "code": "/system/role", "module": "system", "description": "角色管理模块"},
    {"name": "查看角色", "code": "/system/role/read", "module": "system", "description": "查看角色列表和详情"},
    {"name": "创建角色", "code": "/system/role/create", "module": "system", "description": "创建新角色"},
    {"name": "编辑角色", "code": "/system/role/edit", "module": "system", "description": "编辑角色信息"},
    {"name": "删除角色", "code": "/system/role/delete", "module": "system", "description": "删除角色"},
    
    # 权限管理
    {"name": "权限管理", "code": "/system/permission", "module": "system", "description": "权限管理模块"},
    {"name": "查看权限", "code": "/system/permission/read", "module": "system", "description": "查看权限列表和详情"},
    {"name": "创建权限", "code": "/system/permission/create", "module": "system", "description": "创建新权限"},
    {"name": "编辑权限", "code": "/system/permission/edit", "module": "system", "description": "编辑权限信息"},
    {"name": "删除权限", "code": "/system/permission/delete", "module": "system", "description": "删除权限"},
    
    # 菜单管理
    {"name": "菜单管理", "code": "/system/menu", "module": "system", "description": "菜单管理模块"},
    {"name": "查看菜单", "code": "/system/menu/read", "module": "system", "description": "查看菜单列表和详情"},
    {"name": "创建菜单", "code": "/system/menu/create", "module": "system", "description": "创建新菜单"},
    {"name": "编辑菜单", "code": "/system/menu/edit", "module": "system", "description": "编辑菜单信息"},
    {"name": "删除菜单", "code": "/system/menu/delete", "module": "system", "description": "删除菜单"},
]


async def _init_permissions():
    """初始化权限数据"""
    async with AsyncSessionLocal() as db:
        logger.info("正在初始化权限数据...")
        
        permissions = [PermissionCreate(**perm) for perm in DEFAULT_PERMISSIONS]
        created_permissions = await PermissionService.batch_create(db, permissions)
        
        logger.info(f"成功创建 {len(created_permissions)} 个权限")


async def _init_default_organization():
    """初始化默认机构"""
    async with AsyncSessionLocal() as db:
        logger.info("正在初始化默认机构...")
        
        # 检查是否已存在默认机构
        existing_org = await OrganizationService.get_by_code(db, "DEFAULT")
        if existing_org:
            logger.info("默认机构已存在，跳过创建")
            return existing_org
        
        # 创建默认机构
        default_org = OrganizationCreate(
            code="DEFAULT",
            name="默认机构",
            description="系统默认机构，用于初始用户注册",
            status=True
        )
        
        organization = await OrganizationService.create(db, default_org)
        logger.info(f"成功创建默认机构: {organization.name}")
        
        return organization


@app.command()
def init_permissions():
    """
    初始化权限数据
    """
    logger.info("开始初始化权限数据...")
    asyncio.run(_init_permissions())
    logger.info("权限数据初始化完成")


@app.command()
def init_default_organization():
    """
    初始化默认机构
    """
    logger.info("开始初始化默认机构...")
    asyncio.run(_init_default_organization())
    logger.info("默认机构初始化完成")


@app.command()
def init_all():
    """
    初始化所有数据
    """
    logger.info("开始初始化所有数据...")
    asyncio.run(_init_permissions())
    asyncio.run(_init_default_organization())
    logger.info("所有数据初始化完成")


if __name__ == "__main__":
    app()
