[project]
name = "zkm"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi[standard]>=0.115.12",
    "uvicorn>=0.34.2",
    "dynaconf>=3.2.10",
    "pyjwt>=2.10.1",
    "passlib[bcrypt]>=1.7.4",
    "sqlalchemy>=2.0.40",
    "alembic>=1.12.1",
    "aiosqlite>=0.19.0",
    "rich>=14.0.0",
    "typer>=0.9.0",
    "pydantic[email]>=2.11.4",
]

[tool.mypy]
ignore_missing_imports = true

[tool.ruff]
line-length = 120
